{% extends "base.html" %}

{% block title %}Edit Questions{% endblock %}

{% block content %}
<!-- KaTeX CSS and JavaScript -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.11/dist/katex.min.css" integrity="sha384-nB0miv6/jRmo5UMMR1wu3Gz6NLsoTkbqJghGIsx//Rlm+ZU03BU6SQNC66uf4l5+" crossorigin="anonymous">
<script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.11/dist/katex.min.js" integrity="sha384-7zkQWkzuo3B5mTepMUcHkMB5jZaolc2xDwL6VFqjFALcbeS9Ggm/Yr2r3Dy4lfFg" crossorigin="anonymous"></script>
<script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.11/dist/contrib/auto-render.min.js" integrity="sha384-43gviWU0YVjaDtb/GhzOouOXtZMP/7XUzwPTstBeZFe/+rCMvRwr4yROQP43s0Xk" crossorigin="anonymous"></script>

<!-- CKEditor with Image Support - Using a custom build with SimpleUploadAdapter -->
<script src="https://cdn.ckeditor.com/ckeditor5/40.0.0/classic/ckeditor.js"></script>

<style>
    .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
    }

    .header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px;
        border-radius: 10px;
        margin-bottom: 30px;
        text-align: center;
    }

    .questions-list {
        display: block;
    }

    .question-edit {
        display: none;
    }

    .question-card {
        background: white;
        border: 1px solid #e0e0e0;
        border-radius: 10px;
        margin-bottom: 20px;
        padding: 20px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
    }

    .question-card:hover {
        box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        transform: translateY(-2px);
    }

    .question-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
        padding-bottom: 10px;
        border-bottom: 2px solid #f0f0f0;
    }

    .question-number {
        background: #667eea;
        color: white;
        padding: 8px 15px;
        border-radius: 20px;
        font-weight: bold;
        font-size: 14px;
    }

    .edit-btn {
        background: #28a745;
        color: white;
        border: none;
        padding: 8px 20px;
        border-radius: 20px;
        cursor: pointer;
        font-size: 14px;
        transition: all 0.3s ease;
    }

    .edit-btn:hover {
        background: #218838;
        transform: scale(1.05);
    }

    .direction-section {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
    }

    .direction-title {
        font-weight: bold;
        color: #000;
        margin-bottom: 10px;
        font-size: 16px;
    }

    .direction-text {
        color: #000;
        line-height: 1.6;
    }

    .question-options-section {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;
    }

    .question-title {
        color: #000;
        margin-bottom: 15px;
    }

    .question-text {
        color: #000;
        line-height: 1.6;
        margin-bottom: 20px;
        padding: 10px;
        background: white;
        border: 1px solid #dee2e6;
        border-radius: 5px;
    }

    .options-container {
        margin-top: 15px;
    }

    .options-title {
        font-weight: bold;
        color: #000;
        margin-bottom: 10px;
        font-size: 14px;
    }

    .option-item {
        margin-bottom: 8px;
        padding: 8px 12px;
        background: white;
        border-radius: 5px;
        border: 1px solid #dee2e6;
        color: #000;
    }

    .answer-marks-section {
        display: flex;
        gap: 20px;
        margin-top: 15px;
    }

    .answer-info, .marks-info {
        flex: 1;
        padding: 10px;
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 5px;
    }

    .answer-info strong, .marks-info strong {
        color: #000;
    }

    .loading {
        text-align: center;
        padding: 50px;
        font-size: 18px;
        color: #666;
    }

    .error {
        background: #f8d7da;
        color: #721c24;
        padding: 15px;
        border-radius: 5px;
        margin: 20px 0;
        border: 1px solid #f5c6cb;
    }

    .back-btn {
        background: #6c757d;
        color: white;
        border: none;
        padding: 10px 25px;
        border-radius: 25px;
        cursor: pointer;
        font-size: 16px;
        margin-bottom: 20px;
        transition: all 0.3s ease;
    }

    .back-btn:hover {
        background: #5a6268;
        transform: scale(1.05);
    }

    .form-group {
        margin-bottom: 20px;
    }

    .form-group label {
        display: block;
        margin-bottom: 8px;
        font-weight: bold;
        color: #333;
    }

    .form-group input, .form-group textarea {
        width: 100%;
        padding: 10px;
        border: 1px solid #ddd;
        border-radius: 5px;
        font-size: 14px;
        box-sizing: border-box;
    }

    .form-group textarea {
        min-height: 100px;
        resize: vertical;
    }

    .save-btn {
        background: #007bff;
        color: white;
        border: none;
        padding: 12px 30px;
        border-radius: 25px;
        cursor: pointer;
        font-size: 16px;
        margin-right: 10px;
        transition: all 0.3s ease;
    }

    .save-btn:hover {
        background: #0056b3;
        transform: scale(1.05);
    }

    .options-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 15px;
        margin-bottom: 20px;
    }

    .marks-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 15px;
    }

    /* Ensure proper container sizing */
    .question-card {
        overflow: hidden;
    }

    .form-group {
        overflow: hidden;
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
        .options-grid {
            grid-template-columns: 1fr;
        }

        .marks-grid {
            grid-template-columns: 1fr;
        }

        .answer-marks-section {
            flex-direction: column;
        }
    }

    .ck-editor__editable {
        min-height: 150px;
    }

    .ck-editor {
        width: 100% !important;
    }

    .ck-editor .ck-editor__main {
        width: 100% !important;
    }

    .ck-content {
        width: 100% !important;
        box-sizing: border-box !important;
    }

    .math-helper {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 5px;
        padding: 10px;
        margin-bottom: 15px;
        font-size: 12px;
        color: #666;
    }

    .math-helper h4 {
        margin: 0 0 8px 0;
        color: #333;
        font-size: 13px;
    }

    .math-helper code {
        background: #e9ecef;
        padding: 2px 4px;
        border-radius: 3px;
        font-family: 'Courier New', monospace;
    }
    pre{
        white-space: break-spaces !important;
        line-height: 1.6 !important;
    }
</style>

<div class="container">
    <div class="header">
        <div style="display: flex; justify-content: space-between; align-items: center;">
            <div>
                <h1>Edit Questions</h1>
                <p>Manage and edit extracted questions from the document</p>
            </div>
            <button id="backToDocumentsBtn" class="back-btn" style="margin: 0;">
                ← Back to Documents
            </button>
        </div>
    </div>

    <!-- Questions List View -->
    <div id="questionsListView" class="questions-list">
        <div id="loadingMessage" class="loading">
            Loading questions...
        </div>
        <div id="errorMessage" class="error" style="display: none;"></div>
        <div id="questionsContainer"></div>
    </div>

    <!-- Question Edit View -->
    <div id="questionEditView" class="question-edit">
        <button id="backToListBtn" class="back-btn">← Back to Questions List</button>

        <div class="question-card">

            <form id="editQuestionForm">
                <input type="hidden" id="editQuestionId" name="questionId">

                <div class="form-group">
                    <label for="editDirection">Direction (if applicable):</label>
                    <textarea id="editDirection" name="direction" placeholder="Enter directions for this question (optional)"></textarea>
                </div>

                <div class="form-group">
                    <label for="editQuestion">Question:</label>
                    <textarea id="editQuestion" name="question" placeholder="Enter the question text"></textarea>
                </div>

                <div class="form-group">
                    <label for="editQuestionType">Question Type:</label>
                    <input type="text" id="editQuestionType" name="question_type" placeholder="e.g., MCQ">
                </div>

                <div class="options-grid">
                    <div class="form-group">
                        <label for="editOption1">Option 1:</label>
                        <textarea id="editOption1" name="option1" placeholder="Enter option 1"></textarea>
                    </div>
                    <div class="form-group">
                        <label for="editOption2">Option 2:</label>
                        <textarea id="editOption2" name="option2" placeholder="Enter option 2"></textarea>
                    </div>
                    <div class="form-group">
                        <label for="editOption3">Option 3:</label>
                        <textarea id="editOption3" name="option3" placeholder="Enter option 3"></textarea>
                    </div>
                    <div class="form-group">
                        <label for="editOption4">Option 4:</label>
                        <textarea id="editOption4" name="option4" placeholder="Enter option 4"></textarea>
                    </div>
                </div>

                <div class="form-group">
                    <label for="editOption5">Option 5 (if applicable):</label>
                    <textarea id="editOption5" name="option5" placeholder="Enter option 5 (optional)"></textarea>
                </div>

                <div class="form-group">
                    <label for="editAnswer">Answer:</label>
                    <input type="text" id="editAnswer" name="answer" placeholder="Enter the correct answer">
                </div>

                <div class="marks-grid">
                    <div class="form-group">
                        <label for="editMarks">Marks:</label>
                        <input type="number" id="editMarks" name="marks" step="0.1" placeholder="Enter marks">
                    </div>
                    <div class="form-group">
                        <label for="editNegativeMarks">Negative Marks:</label>
                        <input type="number" id="editNegativeMarks" name="negative_mark" step="0.1" placeholder="Enter negative marks">
                    </div>
                </div>

                <div class="form-group">
                    <label for="editTopic">Topic:</label>
                    <input type="text" id="editTopic" name="topic" placeholder="Enter topic">
                </div>

                <div class="form-group">
                    <label for="editSubtopic">Subtopic:</label>
                    <input type="text" id="editSubtopic" name="subtopic" placeholder="Enter subtopic">
                </div>

                <div class="form-group">
                    <label for="editSolution">Solution:</label>
                    <textarea id="editSolution" name="solution" placeholder="Enter the solution explanation (optional)"></textarea>
                </div>

                <button type="submit" class="save-btn">Save Changes</button>
                <button type="button" id="cancelEditBtn" class="back-btn">Cancel</button>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Get the base URL and document ID from the current location
const BASE_URL = window.location.origin;
const DOCUMENT_ID = {{ document_id }};
const EXAM_ID = {{ exam_id if exam_id else 'null' }};

// Global variables
let questions = [];
let currentQuestionIndex = -1;
let directionEditor, questionEditor, option1Editor, option2Editor, option3Editor, option4Editor, option5Editor, solutionEditor;
let examId = EXAM_ID;

// Initialize the page
document.addEventListener('DOMContentLoaded', function() {
    loadQuestions();
    initializeEventListeners();
});

// Initialize event listeners
function initializeEventListeners() {
    document.getElementById('backToListBtn').addEventListener('click', showQuestionsList);
    document.getElementById('cancelEditBtn').addEventListener('click', showQuestionsList);
    document.getElementById('editQuestionForm').addEventListener('submit', handleSaveQuestion);
    document.getElementById('backToDocumentsBtn').addEventListener('click', goBackToDocuments);
}

// Load questions from API
async function loadQuestions() {
    try {
        const response = await fetch(`${BASE_URL}/pyqs_admin/api/documents/${DOCUMENT_ID}/questions`);
        const result = await response.json();

        if (result.success) {
            questions = result.questions;
            displayQuestions();
        } else {
            showError('Failed to load questions: ' + result.error);
        }
    } catch (error) {
        console.error('Error loading questions:', error);
        showError('Error loading questions: ' + error.message);
    }
}

// Navigate back to documents page
function goBackToDocuments() {
    if (examId && examId !== null) {
        window.location.href = `${BASE_URL}/pyqs_admin/exams/${examId}/documents`;
    } else {
        // Fallback: go to exams list
        window.location.href = `${BASE_URL}/pyqs_admin/exams`;
    }
}

// Display questions in the list view
function displayQuestions() {
    const loadingMessage = document.getElementById('loadingMessage');
    const questionsContainer = document.getElementById('questionsContainer');

    loadingMessage.style.display = 'none';

    if (questions.length === 0) {
        questionsContainer.innerHTML = '<div class="error">No questions found for this document.</div>';
        return;
    }

    let html = '';
    questions.forEach((question, index) => {
        html += createQuestionCard(question, index);
    });

    questionsContainer.innerHTML = html;

    // Render math in all question cards
    renderMathInElement(questionsContainer, {
        delimiters: [
            {left: '$$', right: '$$', display: true},
            {left: '$', right: '$', display: false},
            {left: '\\(', right: '\\)', display: false},
            {left: '\\[', right: '\\]', display: true}
        ],
        throwOnError: false,
        output: 'html',
        ignoredTags: []
    });
}

// Create HTML for a question card
function createQuestionCard(question, index) {
    let html = `
        <div class="question-card">
            <div class="question-header">
                <button class="edit-btn" onclick="editQuestion(${index})">Edit Question</button>
            </div>
    `;

    // Add directions if present
    if (question.direction && question.direction.directions) {
        html += `
            <div class="direction-section">
                <div class="direction-title">Directions:</div>
                <pre class="direction-text">${question.direction.directions}</pre>
            </div>
        `;
    }

    // Add question and options in one section
    html += `
        <div class="question-options-section">
            <pre class="question-title">Q${index + 1}. ${question.question || 'No question text'}</pre>
    `;

    // Add options if present
    if (question.option1 || question.option2 || question.option3 || question.option4 || question.option5) {
        html += `<div class="options-container">`;

        ['option1', 'option2', 'option3', 'option4', 'option5'].forEach((optionKey, optIndex) => {
            if (question[optionKey]) {
                const optionLabel = String.fromCharCode(65 + optIndex); // A, B, C, D, E
                html += `<pre class="option-item"><strong>${optionLabel}.</strong> ${question[optionKey]}</pre>`;
            }
        });

        html += `</div>`;
    }

    html += `</div>`;

    // Add answer and marks info
    html += `
        <div class="answer-marks-section">
            <pre class="answer-info">
                <strong>Answer:</strong> ${question.answer || 'Not specified'}
            </pre>
            <div class="marks-info">
                <strong>Marks:</strong> ${question.marks || 'Not specified'}
                ${question.negative_mark ? ` | <strong>Negative:</strong> ${question.negative_mark}` : ''}
            </div>
        </div>
    `;

    // Add solution info if present
    if (question.solution) {
        html += `
            <div class="solution-section" style="margin-top: 15px; padding: 15px; background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 8px;">
                <div style="font-weight: bold; color: #000; margin-bottom: 10px; font-size: 16px;">Solution:</div>
                <pre style="color: #000; line-height: 1.6;">${question.solution}</pre>
            </div>
        `;
    }

    html += `</div>`;
    return html;
}

// Show error message
function showError(message) {
    const errorMessage = document.getElementById('errorMessage');
    const loadingMessage = document.getElementById('loadingMessage');

    loadingMessage.style.display = 'none';
    errorMessage.textContent = message;
    errorMessage.style.display = 'block';
}

// Edit question function
function editQuestion(index) {
    currentQuestionIndex = index;
    const question = questions[index];

    // Populate form fields
    document.getElementById('editQuestionId').value = question.id;
    document.getElementById('editQuestionType').value = question.question_type || '';
    document.getElementById('editAnswer').value = question.answer || '';
    document.getElementById('editMarks').value = question.marks || '';
    document.getElementById('editNegativeMarks').value = question.negative_mark || '';
    document.getElementById('editTopic').value = question.topic || '';
    document.getElementById('editSubtopic').value = question.subtopic || '';

    // Initialize CKEditor for question and options
    initializeCKEditors(question);

    // Show edit view
    showQuestionEdit();
}

// Custom Upload Adapter for CKEditor
class CustomUploadAdapter {
    constructor(loader) {
        this.loader = loader;
    }

    upload() {
        return this.loader.file
            .then(file => new Promise((resolve, reject) => {
                this._initRequest();
                this._initListeners(resolve, reject, file);
                this._sendRequest(file);
            }));
    }

    abort() {
        if (this.xhr) {
            this.xhr.abort();
        }
    }

    _initRequest() {
        const xhr = this.xhr = new XMLHttpRequest();
        xhr.open('POST', `${BASE_URL}/pyqs_admin/upload-image`, true);
        xhr.responseType = 'json';
        xhr.withCredentials = true;
        xhr.setRequestHeader('X-Requested-With', 'XMLHttpRequest');
    }

    _initListeners(resolve, reject, file) {
        const xhr = this.xhr;
        const loader = this.loader;
        const genericErrorText = `Couldn't upload file: ${file.name}.`;

        xhr.addEventListener('error', () => reject(genericErrorText));
        xhr.addEventListener('abort', () => reject());
        xhr.addEventListener('load', () => {
            const response = xhr.response;

            if (!response || xhr.status !== 200) {
                return reject(response && response.error && response.error.message ? response.error.message : genericErrorText);
            }

            if (!response.url) {
                return reject(response.error && response.error.message ? response.error.message : genericErrorText);
            }

            resolve({
                default: response.url
            });
        });

        if (xhr.upload) {
            xhr.upload.addEventListener('progress', evt => {
                if (evt.lengthComputable) {
                    loader.uploadTotal = evt.total;
                    loader.uploaded = evt.loaded;
                }
            });
        }
    }

    _sendRequest(file) {
        const data = new FormData();
        data.append('upload', file);
        this.xhr.send(data);
    }
}

function CustomUploadAdapterPlugin(editor) {
    editor.plugins.get('FileRepository').createUploadAdapter = (loader) => {
        return new CustomUploadAdapter(loader);
    };
}

// Initialize CKEditor instances
function initializeCKEditors(question) {
    // Destroy existing editors if they exist
    destroyCKEditors();

    // CKEditor configuration with enhanced toolbar including image support
    const editorConfig = {
        extraPlugins: [CustomUploadAdapterPlugin],
        toolbar: [
            'heading', '|',
            'bold', 'italic', 'underline', 'subscript', 'superscript', '|',
            'bulletedList', 'numberedList', '|',
            'outdent', 'indent', '|',
            'link', 'insertTable', '|',
            'uploadImage', '|',
            'specialCharacters', '|',
            'undo', 'redo',
        ],
        placeholder: 'Enter text here. For math formulas, use LaTeX syntax like $x^2$ or $$\\frac{a}{b}$$. You can also use inline math with \\( \\) or display math with \\[ \\]. You can also upload images using the image button.',

        // Image configuration
        image: {
            toolbar: [
                'imageTextAlternative', '|',
                'imageStyle:inline', 'imageStyle:block', 'imageStyle:side'
            ]
        }
    };

    // Initialize direction editor
    ClassicEditor
        .create(document.querySelector('#editDirection'), editorConfig)
        .then(editor => {
            directionEditor = editor;
            // Set direction data from question.direction.directions if available
            const directionText = question.direction && question.direction.directions ? question.direction.directions : '';
            editor.setData(directionText);

            console.log('Direction editor initialized successfully with custom upload adapter');
        })
        .catch(error => {
            console.error('Error initializing direction editor:', error);
        });

    // Initialize question editor
    ClassicEditor
        .create(document.querySelector('#editQuestion'), editorConfig)
        .then(editor => {
            questionEditor = editor;
            editor.setData(question.question || '');

            console.log('Question editor initialized successfully with custom upload adapter');
        })
        .catch(error => {
            console.error('Error initializing question editor:', error);
        });

    // Initialize option editors
    const optionFields = ['editOption1', 'editOption2', 'editOption3', 'editOption4', 'editOption5'];
    const optionKeys = ['option1', 'option2', 'option3', 'option4', 'option5'];
    const editors = [null, null, null, null, null];

    optionFields.forEach((fieldId, index) => {
        ClassicEditor
            .create(document.querySelector(`#${fieldId}`), editorConfig)
            .then(editor => {
                editors[index] = editor;
                editor.setData(question[optionKeys[index]] || '');

                // Store editor references
                if (index === 0) option1Editor = editor;
                else if (index === 1) option2Editor = editor;
                else if (index === 2) option3Editor = editor;
                else if (index === 3) option4Editor = editor;
                else if (index === 4) option5Editor = editor;

                console.log(`${fieldId} editor initialized successfully with custom upload adapter`);
            })
            .catch(error => {
                console.error(`Error initializing ${fieldId} editor:`, error);
            });
    });

    // Initialize solution editor
    ClassicEditor
        .create(document.querySelector('#editSolution'), editorConfig)
        .then(editor => {
            solutionEditor = editor;
            editor.setData(question.solution || '');

            console.log('Solution editor initialized successfully with custom upload adapter');
        })
        .catch(error => {
            console.error('Error initializing solution editor:', error);
        });
}

// Destroy CKEditor instances
function destroyCKEditors() {
    if (directionEditor) {
        directionEditor.destroy();
        directionEditor = null;
    }
    if (questionEditor) {
        questionEditor.destroy();
        questionEditor = null;
    }
    if (option1Editor) {
        option1Editor.destroy();
        option1Editor = null;
    }
    if (option2Editor) {
        option2Editor.destroy();
        option2Editor = null;
    }
    if (option3Editor) {
        option3Editor.destroy();
        option3Editor = null;
    }
    if (option4Editor) {
        option4Editor.destroy();
        option4Editor = null;
    }
    if (option5Editor) {
        option5Editor.destroy();
        option5Editor = null;
    }
    if (solutionEditor) {
        solutionEditor.destroy();
        solutionEditor = null;
    }
}

// Show questions list view
function showQuestionsList() {
    destroyCKEditors();
    document.getElementById('questionsListView').style.display = 'block';
    document.getElementById('questionEditView').style.display = 'none';
}

// Show question edit view
function showQuestionEdit() {
    document.getElementById('questionsListView').style.display = 'none';
    document.getElementById('questionEditView').style.display = 'block';
}

// Handle save question
async function handleSaveQuestion(event) {
    event.preventDefault();

    if (currentQuestionIndex === -1) {
        alert('No question selected for editing');
        return;
    }

    const questionId = document.getElementById('editQuestionId').value;

    // Collect form data
    const formData = {
        directions: directionEditor ? directionEditor.getData() : '',
        question: questionEditor ? questionEditor.getData() : '',
        question_type: document.getElementById('editQuestionType').value,
        option1: option1Editor ? option1Editor.getData() : '',
        option2: option2Editor ? option2Editor.getData() : '',
        option3: option3Editor ? option3Editor.getData() : '',
        option4: option4Editor ? option4Editor.getData() : '',
        option5: option5Editor ? option5Editor.getData() : '',
        answer: document.getElementById('editAnswer').value,
        marks: document.getElementById('editMarks').value,
        negative_mark: document.getElementById('editNegativeMarks').value,
        topic: document.getElementById('editTopic').value,
        subtopic: document.getElementById('editSubtopic').value,
        solution: solutionEditor ? solutionEditor.getData() : ''
    };

    try {
        const response = await fetch(`${BASE_URL}/pyqs_admin/solutions/${questionId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(formData)
        });

        const result = await response.json();

        if (result.success) {
            alert('Question updated successfully!');

            // Update the question in our local array
            questions[currentQuestionIndex] = {
                ...questions[currentQuestionIndex],
                ...formData,
                // Update direction structure to match expected format
                direction: formData.directions ? { directions: formData.directions } : null
            };

            // Go back to list view and refresh display
            showQuestionsList();
            displayQuestions();
        } else {
            alert('Error updating question: ' + result.error);
        }
    } catch (error) {
        console.error('Error saving question:', error);
        alert('Error saving question: ' + error.message);
    }
}
</script>
{% endblock %}